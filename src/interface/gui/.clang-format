---
Language:        Cpp
BasedOnStyle: Google
AlignConsecutiveMacros: 'true'
AlignConsecutiveAssignments: 'true'
AllowShortCaseLabelsOnASingleLine: 'true'
AllowShortBlocksOnASingleLine: 'Empty'
AllowShortIfStatementsOnASingleLine: 'Never'
AllowShortLoopsOnASingleLine: 'false'
BreakBeforeBraces: Custom
Cpp11BracedListStyle: 'false'
ColumnLimit: 125 
NamespaceIndentation: All
SpaceAfterTemplateKeyword: 'false'
SpaceBeforeCtorInitializerColon: 'true'
SpaceBeforeInheritanceColon: 'true'
SpaceBeforeRangeBasedForLoopColon: 'true'
SpaceInEmptyBlock: true
Standard: 'Latest'
...
