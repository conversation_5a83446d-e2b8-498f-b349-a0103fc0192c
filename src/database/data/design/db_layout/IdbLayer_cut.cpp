// ***************************************************************************************
// Copyright (c) 2023-2025 Peng Cheng Laboratory
// Copyright (c) 2023-2025 Institute of Computing Technology, Chinese Academy of Sciences
// Copyright (c) 2023-2025 Beijing Institute of Open Source Chip
//
// iEDA is licensed under Mulan PSL v2.
// You can use this software according to the terms and conditions of the Mulan PSL v2.
// You may obtain a copy of Mulan PSL v2 at:
// http://license.coscl.org.cn/MulanPSL2
//
// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
// EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
// MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
//
// See the Mulan PSL v2 for more details.
// ***************************************************************************************
/**
 * @project		iDB
 * @file		IdbLayer.h
 * @date		25/05/2021
 * @version		0.1
* @description


        Describe Tech Layer information,.
 *
 */

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
#include <algorithm>

#include "IdbLayer.h"
#include "IdbPropertyCutSpacing.h"

namespace idb {
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**
 * @date		25/05/2021
 * @version		0.1
* @description


        Describe Enclosure info.
 *
 */
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
IdbLayerCutEnclosure::IdbLayerCutEnclosure()
{
  _overhang_1 = -1;
  _overhang_2 = -1;
}

IdbLayerCutEnclosure::~IdbLayerCutEnclosure()
{
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
IdbLayerCutArraySpacing::IdbLayerCutArraySpacing()
{
  _cut_spacing = -1;
}

IdbLayerCutArraySpacing::~IdbLayerCutArraySpacing()
{
  _array_cut_list.clear();
  std::vector<IdbArrayCut>().swap(_array_cut_list);
}

bool IdbLayerCutArraySpacing::set_array_value(int32_t index, int32_t array_cut, int32_t array_spacing)
{
  if ((static_cast<int>(_array_cut_list.size()) >= index) && (index >= 0)) {
    IdbArrayCut array_cut_new;
    array_cut_new._array_cut = array_cut;
    array_cut_new._array_spacing = array_spacing;
    _array_cut_list[index] = array_cut_new;

    return true;
  }

  return false;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**
 * @date		25/05/2021
 * @version		0.1
* @description


        Describe Cut Layer.
 *
 */

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
IdbLayerCut::IdbLayerCut()
{
  _width = -1;
  // _spacing          = -1;
  _enclosure_below = new IdbLayerCutEnclosure();
  _enclosure_above = new IdbLayerCutEnclosure();
  _array_spacing = new IdbLayerCutArraySpacing();
  _via_rule_default = nullptr;
  _via_rule = nullptr;
  set_type(IdbLayerType::kLayerCut);

  _cut_spacing_list = new IdbRuleCutSpacingList();
}

IdbLayerCut::~IdbLayerCut()
{
  if (_enclosure_above) {
    delete _enclosure_above;
    _enclosure_above = nullptr;
  }

  if (_enclosure_below) {
    delete _enclosure_below;
    _enclosure_below = nullptr;
  }

  if (_array_spacing) {
    delete _array_spacing;
    _array_spacing = nullptr;
  }

  if (_cut_spacing_list) {
    delete _cut_spacing_list;
    _cut_spacing_list = nullptr;
  }
  for (auto* spacing : _spacings) {
    if (spacing) {
      delete spacing;
    }
  }
}

}  // namespace idb
