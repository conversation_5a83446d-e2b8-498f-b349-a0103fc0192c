/*
Gutengerg Post Parser, the C bindings.

Warning, this file is autogenerated by `cbindgen`.
Do not modify this manually.

*/

#include <stdarg.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

/**
 * liberty expression operation.
 */
typedef enum LibertyExprOp {
    <PERSON><PERSON><PERSON>,
    Not,
    Or,
    And,
    Xor,
    One,
    Zero,
    Plus,
    Mult,
} LibertyExprOp;

/**
 * The group statement.
 * # Example
 *
 * wire_load("5K_hvratio_1_1") {
 * capacitance : 1.774000e-01;
 * resistance : 3.571429e-03;
 * slope : 5.000000;
 * fanout_length( 1, 1.7460 );
 * fanout_length( 2, 3.9394 );
 * fanout_length( 3, 6.4626 );
 * fanout_length( 4, 9.2201 );
 * fanout_length( 5, 11.9123 );
 * fanout_length( 6, 14.8358 );
 * fanout_length( 7, 18.6155 );
 * fanout_length( 8, 22.6727 );
 * fanout_length( 9, 25.4842 );
 * fanout_length( 11, 27.0320 );
 * }
 */
typedef struct LibertyGroupStmt LibertyGroupStmt;

typedef struct Option_String Option_String;

typedef struct RustVec {
    void *data;
    uintptr_t len;
    uintptr_t cap;
    uintptr_t type_size;
} RustVec;

typedef struct RustStingView {
    const uint8_t *data;
    uintptr_t len;
} RustStingView;

typedef struct RustLibertyStringValue {
    char *value;
} RustLibertyStringValue;

typedef struct RustLibertyFloatValue {
    double value;
} RustLibertyFloatValue;

typedef struct RustLibertyGroupStmt {
    char *file_name;
    uintptr_t line_no;
    char *group_name;
    struct RustVec attri_values;
    struct RustVec stmts;
} RustLibertyGroupStmt;

typedef struct RustLibertySimpleAttrStmt {
    char *file_name;
    uintptr_t line_no;
    char *attri_name;
    const void *attri_value;
} RustLibertySimpleAttrStmt;

typedef struct RustLibertyComplexAttrStmt {
    char *file_name;
    uintptr_t line_no;
    char *attri_name;
    struct RustVec attri_values;
} RustLibertyComplexAttrStmt;

typedef struct RustLibertyExpr {
    enum LibertyExprOp op;
    void *left;
    void *right;
    char *port_name;
} RustLibertyExpr;

/**
 * liberty expr.
 */
typedef struct LibertyExpr {
    enum LibertyExprOp op;
    struct LibertyExpr *left;
    struct LibertyExpr *right;
    struct Option_String port_name;
} LibertyExpr;

void *rust_parse_lib(const char *lib_path);

void rust_free_lib_group(struct LibertyGroupStmt *c_lib_group);

uintptr_t lib_rust_vec_len(const struct RustVec *vec);

void lib_free_c_char(char *s);

struct RustStingView test_string_to_view(void);

struct RustLibertyStringValue *rust_convert_string_value(void *string_value);

/**
 * strint value converted value should be release by the API.
 */
void rust_free_string_value(struct RustLibertyStringValue *c_string_value);

struct RustLibertyFloatValue *rust_convert_float_value(void *float_value);

void rust_free_float_value(struct RustLibertyFloatValue *c_float_value);

bool rust_is_float_value(void *c_attribute_value);

bool rust_is_string_value(void *c_attribute_value);

struct RustLibertyGroupStmt *rust_convert_raw_group_stmt(struct LibertyGroupStmt *group_stmt);

void rust_free_group_stmt(struct RustLibertyGroupStmt *c_group_stmt);

/**
 * The API differenct form rust_convert_raw_group_stmt is one use thin pointer, this use fat pointer.
 */
struct RustLibertyGroupStmt *rust_convert_group_stmt(struct LibertyGroupStmt *c_group_stmt);

struct RustLibertySimpleAttrStmt *rust_convert_simple_attribute_stmt(void *c_simple_attri_stmt);

void rust_free_simple_attribute_stmt(struct RustLibertySimpleAttrStmt *c_simple_attri_stmt);

struct RustLibertyComplexAttrStmt *rust_convert_complex_attribute_stmt(void *c_complex_attri_stmt);

void rust_free_complex_attribute_stmt(struct RustLibertyComplexAttrStmt *c_complex_attri_stmt);

bool rust_is_simple_attri_stmt(void *c_lib_stmt);

bool rust_is_complex_attri_stmt(void *c_lib_stmt);

bool rust_is_attri_stmt(void *c_lib_stmt);

bool rust_is_group_stmt(void *c_lib_stmt);

void *rust_parse_expr(const char *expr_str);

/**
 * convert expr to c expr.
 */
struct RustLibertyExpr *rust_convert_expr(struct LibertyExpr *c_expr);

/**
 * free c expr after use.
 */
void rust_free_expr(struct RustLibertyExpr *c_expr);
