[package]
name = "liberty-parser"
version = "0.1.0"
edition = "2021"
exclude = ["build"]
rust-version = "1.70"
authors = ["taosimin <<EMAIL>>"]
include = ["**/*.rs", "*.toml", "*.pest", "LICENSE", "README.md"]
git = "https://gitee.com/oscc-project/parser.git"

description = "iEDA liberty parser"
license-file = "LICENSE"
repository = "https://gitee.com/oscc-project/"

[build-dependencies]
cbindgen = "0.26"

[lib]
name = "liberty_parser"
crate-type = ["staticlib"]
#crate-type = ["cdylib"]


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
pest = "2.6"
pest_derive = "2.6"

[profile.dev]
debug = true
