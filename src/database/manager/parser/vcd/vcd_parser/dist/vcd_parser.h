/*
Gutengerg Post Parser, the C bindings.

Warning, this file is autogenerated by `cbindgen`.
Do not modify this manually.

*/

#include <stdarg.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

/**
 * VCD variable type of vcd signal
 */
typedef enum VCDVariableType {
    VarEvent,
    VarInteger,
    VarParameter,
    VarReal,
    VarRealTime,
    VarReg,
    VarSupply0,
    VarSupply1,
    VarTime,
    VarTri,
    VarTriAnd,
    VarTriOr,
    VarTriReg,
    VarTri0,
    VarTri1,
    VarWAnd,
    VarWire,
    VarWor,
    Default,
} VCDVariableType;

typedef struct Rc_RefCell_VCDScope Rc_RefCell_VCDScope;

typedef struct Rc_VCDSignal Rc_VCDSignal;

typedef struct SignalDuration SignalDuration;

typedef struct SignalTC SignalTC;

/**
 * VCD File
 */
typedef struct VCDFile VCDFile;

/**
 * VCD Scope
 */
typedef struct VCDScope VCDScope;

/**
 * VCD signal
 */
typedef struct VCDSignal VCDSignal;

typedef struct RustVec {
    void *data;
    uintptr_t len;
    uintptr_t cap;
    uintptr_t type_size;
} RustVec;

typedef struct RustSignalTC {
    char *signal_name;
    uint64_t signal_tc;
} RustSignalTC;

typedef struct RustSignalDuration {
    char *signal_name;
    uint64_t bit_0_duration;
    uint64_t bit_1_duration;
    uint64_t bit_x_duration;
    uint64_t bit_z_duration;
} RustSignalDuration;

typedef struct Indexes {
    int32_t lindex;
    int32_t rindex;
} Indexes;

typedef struct RustVCDSignal {
    char *hash;
    char *name;
    void *bus_index;
    unsigned int signal_size;
    enum VCDVariableType signal_type;
    void *scope;
} RustVCDSignal;

typedef struct RustVCDScope {
    char *name;
    void *parent_scope;
    struct RustVec children_scope;
    struct RustVec scope_signals;
} RustVCDScope;

typedef struct RustVCDFile {
    long long start_time;
    long long end_time;
    unsigned int time_resolution;
    unsigned int time_unit;
    char *date;
    char *version;
    char *comment;
    void *scope_root;
} RustVCDFile;

typedef struct RustTcAndSpResVecs {
    struct RustVec signal_tc_vec;
    struct RustVec signal_duration_vec;
} RustTcAndSpResVecs;

uintptr_t vcd_rust_vec_len(const struct RustVec *vec);

void vcd_free_c_char(char *s);

struct RustSignalTC *rust_convert_signal_tc(struct SignalTC *c_signal_tc);

struct RustSignalDuration *rust_convert_signal_duration(struct SignalDuration *c_signal_duration);

struct Indexes *rust_convert_signal_index(void *bus_index);

struct RustVCDSignal *rust_convert_vcd_signal(const struct VCDSignal *c_vcd_signal);

void *rust_convert_rc_ref_cell_scope(const struct Rc_RefCell_VCDScope *c_data);

const void *rust_convert_rc_ref_cell_signal(const struct Rc_VCDSignal *c_data);

struct RustVCDScope *rust_convert_vcd_scope(const struct VCDScope *c_vcd_scope);

struct RustVCDFile *rust_convert_vcd_file(struct VCDFile *c_vcd_file);

void *rust_parse_vcd(const char *lib_path);

struct RustTcAndSpResVecs *rust_calc_scope_tc_sp(const char *c_top_vcd_scope_name,
                                                 struct VCDFile *c_vcd_file);

struct RustVCDScope *find_scope_by_name(const char *scope_name, struct VCDFile *c_vcd_file);

struct RustVCDSignal *find_signal_by_name(const char *scope_name, struct VCDFile *c_vcd_file);
