WHITESPACE = _{ " " | "\t" | "\r" | "\n" }

/* header */

scale_unit   = { "s" | "ms" | "us" | "ns" | "ps" | "fs" }
scale_number = { ASCII_DIGIT+ }
scale_text   = { scale_number ~ scale_unit }

header_text = _{ (!"$" ~ ANY)+ }

date_text =  { header_text }
date      = _{ "$date" ~ date_text ~ "$end" }

version_text = { header_text }
version      = _{ "$version" ~ version_text ~ "$end" }

timescale = _{ "$timescale" ~ scale_text ~ "$end" }

comment_text =  { header_text }
vcd_comment  = _{ "$comment" ~ comment_text ~ "$end" }

header_section = _{
    date ~ version? ~ timescale ~ vcd_comment?
}

/* definition */
module_var_name = @{ (ASCII_ALPHA | "_") ~ (ASCII_ALPHA | ASCII_DIGIT | "_" | "(" | ")" | "." | "[" | "]" | "/" | "$" | "*")* }

variable_type   = {
    "event"
  | "integer"
  | "parameter"
  | "real"
  | "realtime"
  | "reg"
  | "supply0"
  | "supply1"
  | "time"
  | "tri"
  | "triand"
  | "trior"
  | "trireg"
  | "tri0"
  | "tri1"
  | "wand"
  | "wire"
  | "wor"
}
variable_number = @{ ASCII_DIGIT+ }
variable_ref    = @{
    (ASCII_ALPHA | ASCII_DIGIT | "!" | "/" | "," | "@" | "'" | ":" | "~" | "#" | "*" | "(" | ")" | "+" | "{" | "}" | "$" | "%" | "[" | "]" | "`" | "\"" | "&" | ";" | "<" | ">" | "=" | "?" | "-" | "^" | "|" | "\\")+
}

bus_slice_left  = @{ ASCII_DIGIT+ }
bus_slice_right = @{ ASCII_DIGIT+ }

bus_slice = { "[" ~ bus_slice_left ~ (":" ~ bus_slice_right)? ~ "]" }

variable = {
    "$var" ~ variable_type ~ variable_number ~ variable_ref ~ module_var_name ~ (bus_slice)? ~ "$end"
}

scope_type = { "module" | "begin" | "fork" | "function" | "task" }

open_scope  = { "$scope" ~ scope_type ~ module_var_name ~ "$end" }
close_scope = { "$upscope" ~ "$end" }

scope  = _{ open_scope ~ (scope | variable)+ ~ close_scope }
scopes = _{ scope+ }

variable_definition_section = _{ scopes ~ "$enddefinitions" ~ "$end" ~ vcd_comment?}

/* change */
scalar_num = { "0" | "1" | "x" | "X" | "z" | "Z" }

bin_num_text =  @{ scalar_num+ }
bin_num      = _{ ("b" | "B") ~ bin_num_text }
real_num     =  {
    ("r" | "R") ~ ("-")? ~ ASCII_DIGIT+ ~ (("e" | "E") ~ ("+" | "-")? ~ ASCII_DIGIT+)?
}

simulation_keyword = _{ "$dumpall" | "$dumpoff" | "$dumpon" | "$dumpvars" }
simulation_time    = @{ "#" ~ ASCII_DIGIT+ }

scalar_value_change    =  { scalar_num ~ variable_ref }
bitvector_value_change =  { bin_num ~ variable_ref }
real_value_change      =  { real_num ~ variable_ref }
vector_value_change    = _{ bitvector_value_change | real_value_change }

signal_value_change = _{ scalar_value_change | vector_value_change }

value_change         = _{
    simulation_time? ~ simulation_keyword ~ (signal_value_change)* ~ "$end"
  | simulation_time ~ (signal_value_change)*
}
value_change_section = _{ value_change+ }

vcd_file_text = { header_section ~ variable_definition_section ~ value_change_section }

vcd_file = _{
    SOI ~ vcd_file_text ~ EOI
}
