decimal_digits  = _{ ASCII_DIGIT }
// bus_index  = _{ "[" ~ decimal_digits+ ~ "]" }
range_from = { decimal_digits+ }
range_to = { decimal_digits+ }
bus_slice  = { "[" ~ range_from ~ ":" ~ range_to ~ "]" }

scalar_constant = { "1'b0" | "1'b1" | "1'h0" | "1'h1" | "1'B0" | "1'B1" | "'b0" | "'b1" | "'B0" | "'B1" | "1" | "0" | "2'b00"}

line_comment      = _{ "//" ~ (!("\n") ~ ASCII)* ~ ("\n" | EOI) }
multiline_comment = _{ "/*" ~ (!"*/" ~ ANY)* ~ "*/" }
COMMENT = _{ line_comment | multiline_comment }

no_equal_char = _{ASCII_ALPHANUMERIC | "_" | "\\" | "[" | "]" | "/" | ":" | "$" | "." | "'" | "-"}
char = _{ no_equal_char | "=" }

// Donnot deal with yosys with (* top =  1  *)
yosys_hierarchy_declaration = {"(* top =" ~ decimal_digits ~ "*)" }

//(assign_port_or_wire_id donnot contain the whitespace.)
assign_port_or_wire_id = @{ (char+ ~ " " ~ no_equal_char+) | char+ } 
port_or_wire_id = @{ (char+ ~ " " ~ char+) | char+ }
port_list = { port_or_wire_id ~ ("," ~ port_or_wire_id)* }
// To do (.v before DC)
// port_bus_list 
wire_list = { port_or_wire_id ~ ("," ~ port_or_wire_id)* }
first_wire_list_with_scalar_constant = _{ scalar_constant | port_or_wire_id }
next_wire_list_with_scalar_constant = _{ (("," ~ scalar_constant) | ("," ~ port_or_wire_id))* }
wire_list_with_scalar_constant = _{ first_wire_list_with_scalar_constant ~ next_wire_list_with_scalar_constant }

cell_id = { char+ }
inst_id =  @{ (char+ ~ " " ~ char+) | char+  }
module_id = { char+ }
WHITESPACE = _{ " " | "\t" | "\r" | "\n" }
semicolon_opt = _{ ";" }

input_declaration = { "input" ~ bus_slice? ~ port_list  ~ semicolon_opt }
output_declaration = { "output" ~ bus_slice? ~ port_list ~ semicolon_opt }
inout_declaration = { "inout" ~ bus_slice? ~ port_list ~ semicolon_opt }
port_declaration = _{ input_declaration | output_declaration | inout_declaration }
wire_declaration = {"wire" ~ bus_slice? ~ wire_list ~ semicolon_opt }
port_or_wire_declaration = _{ port_declaration | wire_declaration }
port_or_wire_block_declaration = { port_or_wire_declaration* }

concate_wire_list = _{ "{" ~ wire_list_with_scalar_constant ~ "}" }

assign_declaration = { "assign" ~ assign_port_or_wire_id ~ "=" ~ (scalar_constant | assign_port_or_wire_id | concate_wire_list) ~ semicolon_opt }

//(ps:first_port_connection) first rule adapt to .REN(n_Logic1_ ) or .REN( ) or .io_interrupt(1'b0)
//(ps:first_port_connection) second rule adapt to .FBDIV({  \u0_rcg/n33 ,  \u0_rcg/n33 ,  \u0_rcg/n33 })
//(ps:first_port_connection) or .io_master_bid({ 1'b0,1'b0,io_master_bid_041461 }) (begin)
//or .io_master_bresp({ io_master_bresp_041461_1_, 1'b0 }) (end) or .rid_nic400_axi4_ps2({ 1'b0,1'b0,rid_nic400_axi4_ps2_1_,1'b0 }) (begin_end)
first_port_connection_single_connect = { "." ~ port_or_wire_id ~ "(" ~ ( scalar_constant | port_or_wire_id )? ~ ")" }
first_port_connection_multiple_connect = { "." ~ port_or_wire_id ~ "(" ~ concate_wire_list ~ ")" }
first_port_connection = _{ first_port_connection_single_connect | first_port_connection_multiple_connect }
port_connection = _{ "," ~ first_port_connection }
port_block_connection = !{ "(" ~ first_port_connection? ~ port_connection* ~ ")"  ~ semicolon_opt }

inst_declaration = ${ cell_id ~ WHITESPACE+ ~ inst_id ~ WHITESPACE* ~ port_block_connection }

assign_or_inst_declaration = _{ assign_declaration | inst_declaration }
assign_or_inst_block_declaration = { assign_or_inst_declaration* }

module_declaration = { yosys_hierarchy_declaration? ~ "module" ~ module_id ~ "(" ~ port_list ~ ");" ~ port_or_wire_block_declaration 
~ assign_or_inst_block_declaration ~ "endmodule" }

verilog_file = _{ SOI ~ module_declaration+ ~ EOI }



