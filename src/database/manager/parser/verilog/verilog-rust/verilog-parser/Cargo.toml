[package]
name = "verilog-parser"
version = "0.1.0"
edition = "2021"
exclude = ["build"]
rust-version = "1.66"
authors = ["longshuaiying <<EMAIL>>"]
include = ["**/*.rs", "*.toml", "*.pest", "LICENSE", "README.md"]
git = "https://gitee.com/oscc-project/parser.git"

description = "iEDA verilog netlist parser"
license-file = "LICENSE"
repository = "https://gitee.com/oscc-project/"

[build-dependencies]
cbindgen = "0.26"

[lib]
name = "verilog_parser"
crate-type = ["staticlib"]
# crate-type = ["cdylib"]


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html


# Dependencies on the Pest parser generator library.
[dependencies]
pest = "2.6"
pest_derive = "2.6"
log = "0.4"

# Config build option
[profile.dev]
debug = true
