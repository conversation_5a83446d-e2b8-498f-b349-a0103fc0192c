#build settings
set(HOME_BUILD ${PROJECT_SOURCE_DIR}/build)

#cmake settings
set(HOME_CMAKE ${PROJECT_SOURCE_DIR}/cmake)

#auxiliary settings
set(HOME_UTILITY ${PROJECT_SOURCE_DIR}/src/utility)

#database settings
set(HOME_DATABASE ${PROJECT_SOURCE_DIR}/src/database)

#feature settings
set(HOME_FEATURE ${PROJECT_SOURCE_DIR}/src/feature)

#interface settings
set(HOME_INTERFACE ${PROJECT_SOURCE_DIR}/src/interface)

#operation settings
set(HOME_OPERATION ${PROJECT_SOURCE_DIR}/src/operation)

#platform settings
set(HOME_PLATFORM ${PROJECT_SOURCE_DIR}/src/platform)

#evaluation settings
set(HOME_EVALUATION ${PROJECT_SOURCE_DIR}/src/evaluation)

#analysis settings
set(HOME_ANALYSIS ${PROJECT_SOURCE_DIR}/src/analysis)

#solver settings
set(HOME_SOLVER ${PROJECT_SOURCE_DIR}/src/solver)

#third party settings
set(HOME_THIRDPARTY ${PROJECT_SOURCE_DIR}/src/third_party)
